import React from "react";
import { render } from "react-dom";
import { Meteor } from "meteor/meteor";
import { Router } from "react-router-dom";
import App from "../imports/ui/routing/App";
import { browserHistory } from "../imports/ui/routing/setupBrowserHistory";
import { AppDataProvider } from "../imports/ui/routing/AppDataContext";
import "/imports/startup/client";
import LogoutTracker from "./logoutTracker";
import { UserContextProvider } from "../imports/contexts/UserContext";
import { StaticDataContextProvider } from "../imports/contexts/StaticDataContext";

// import MockDate from "mockdate";

// MockDate.set("2019-08-02"); // Fall
// MockDate.set("2020-01-02"); // Winter
// MockDate.set("2020-04-02"); // Spring

Meteor.startup(() => {
  render(
    <UserContextProvider>
      <StaticDataContextProvider>
        <Router history={browserHistory}>
          <LogoutTracker>
            <AppDataProvider>
              <App />
            </AppDataProvider>
          </LogoutTracker>
        </Router>
      </StaticDataContextProvider>
    </UserContextProvider>,
    document.getElementById("root")
  );
});
